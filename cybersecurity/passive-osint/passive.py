#!/usr/bin/env python3
"""
passive.py — Passive OSINT CLI

Educational tool for basic, *passive* reconnaissance. It recognizes input type
(full name, IP address, or username) and retrieves publicly available info.

⚠️ Legal & Ethical: Use only with explicit permission and for educational purposes.
"""

import argparse
import ipaddress
import json
import os
import re
import sys
import time
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, Optional, Tuple, List

# Networking utils
try:
    import requests
    from bs4 import BeautifulSoup
except Exception as e:
    print("Missing dependencies. Install with: pip install -r requirements.txt", file=sys.stderr)
    sys.exit(1)

UA = (
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 "
    "(KHTML, like Gecko) Chrome/********* Safari/537.36"
)
TIMEOUT = 10

@dataclass
class NameResult:
    first_name: str
    last_name: str
    address: Optional[str] = None
    phone: Optional[str] = None
    source: Optional[str] = None

@dataclass
class IPResult:
    ip: str
    city: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    isp: Optional[str] = None
    source: Optional[str] = None

@dataclass
class UsernameResult:
    username: str
    services: Dict[str, bool]  # service -> exists?
    source: str = "Direct profile checks (HTTPS)"

def save_result(text: str, base_name: str = "result") -> Path:
    """
    Save results to result.txt, result2.txt, ... resultN.txt
    """
    i = 1
    p = Path(f"{base_name}.txt")
    while p.exists():
        i += 1
        p = Path(f"{base_name}{i}.txt")
    p.write_text(text, encoding="utf-8")
    return p

def is_username(s: str) -> bool:
    # Allow '@' prefix, letters, digits, underscore, dot, hyphen
    return bool(re.fullmatch(r'@?[A-Za-z0-9_.-]{1,30}', s))

def normalize_username(s: str) -> str:
    return s[1:] if s.startswith("@") else s

def is_full_name(s: str) -> bool:
    # Very simple heuristic: 2+ words with letters (can include accents and hyphenation)
    return bool(re.fullmatch(r"[A-Za-zÀ-ÖØ-öø-ÿ'`-]+(?:\s+[A-Za-zÀ-ÖØ-öø-ÿ'`-]+)+", s.strip()))

def split_full_name(s: str) -> Tuple[str, str]:
    parts = s.strip().split()
    if len(parts) >= 2:
        first = parts[0]
        last = " ".join(parts[1:])
        return first, last
    return s.strip(), ""

def is_ip(s: str) -> bool:
    try:
        ipaddress.ip_address(s.strip())
        return True
    except Exception:
        return False

# -------------------- Providers --------------------

def ip_lookup(ip: str) -> Optional[IPResult]:
    """
    Try several free IP geolocation endpoints (no key).
    """
    endpoints = [
        ("ipapi.co", f"https://ipapi.co/{ip}/json/"),
        ("ipinfo.io", f"https://ipinfo.io/{ip}/json"),
        ("ip-api.com", f"http://ip-api.com/json/{ip}?fields=status,city,lat,lon,isp,org,query"),
    ]
    for name, url in endpoints:
        try:
            r = requests.get(url, headers={"User-Agent": UA}, timeout=TIMEOUT)
            if r.status_code != 200:
                continue
            data = r.json()
            # Normalize fields across providers
            city = data.get("city") or data.get("City")
            loc = data.get("loc")
            lat, lon = None, None
            if loc and isinstance(loc, str) and "," in loc:
                lat, lon = [float(x) for x in loc.split(",", 1)]
            else:
                # ip-api variant
                if "lat" in data and "lon" in data:
                    lat, lon = data.get("lat"), data.get("lon")
            isp = (
                data.get("org")
                or data.get("Org")
                or data.get("isp")
                or data.get("ISP")
            )
            return IPResult(ip=ip, city=city, latitude=lat, longitude=lon, isp=isp, source=name)
        except Exception:
            continue
    return None

PHONE_REGEX = re.compile(r"(?:(?:\+|00)\d{1,3}[\s.-]?)?(?:\(?\d{2,4}\)?[\s.-]?)?\d{2,4}[\s.-]?\d{2,4}(?:[\s.-]?\d{2,4})?")
ADDRESS_HINTS = ["rue", "avenue", "av.", "bd", "boulevard", "chemin", "route", "impasse", "allée", "place", "street", "st.", "road", "rd", "lane"]

def find_address_like(text: str) -> Optional[str]:
    # Very naive: look for lines containing address-like words
    lines = [l.strip() for l in re.split(r"[\n\r]", text) if l.strip()]
    for line in lines:
        low = line.lower()
        if any(h in low for h in ADDRESS_HINTS):
            # Pick up postal code if present
            return line
    return None

def directory_lookup_fr(first: str, last: str) -> Optional[Tuple[str, str, str]]:
    """
    Attempt a French directory lookup (pages blanches).
    Returns (address, phone, source) if found.
    """
    targets = [
        ("pagesjaunes", f"https://www.pagesjaunes.fr/pagesblanches/recherche?quoiqui={first}%20{last}"),
        ("118000", f"https://www.118000.fr/search?who={first}%20{last}"),
        ("118712", f"https://annuaire.118712.fr/?who={first}%20{last}"),
    ]
    for source, url in targets:
        try:
            resp = requests.get(url, headers={"User-Agent": UA}, timeout=TIMEOUT)
            if resp.status_code != 200:
                continue
            html = resp.text
            soup = BeautifulSoup(html, "html.parser")
            # Extract visible text
            text = soup.get_text("\n", strip=True)
            # Phone
            phone = None
            m = PHONE_REGEX.search(text)
            if m:
                phone = m.group(0)
            # Address
            address = find_address_like(text)
            if address or phone:
                return (address or "N/A", phone or "N/A", source)
        except Exception:
            continue
    return None

def name_lookup(full_name: str) -> Optional[NameResult]:
    first, last = split_full_name(full_name)
    # Try FR directory first (example input is French)
    hit = directory_lookup_fr(first, last)
    if hit:
        address, phone, source = hit
        return NameResult(first_name=first, last_name=last, address=address, phone=phone, source=source)
    # Fallback: generic web search via DuckDuckGo HTML (no JS)
    try:
        q = f'"{first} {last}" adresse téléphone'
        url = f"https://duckduckgo.com/html/?q={requests.utils.quote(q)}"
        r = requests.get(url, headers={"User-Agent": UA}, timeout=TIMEOUT)
        if r.status_code == 200:
            soup = BeautifulSoup(r.text, "html.parser")
            # Concatenate snippets
            snippets = " ".join([s.get_text(" ", strip=True) for s in soup.select(".result__snippet")])
            phone = None
            m = PHONE_REGEX.search(snippets)
            if m:
                phone = m.group(0)
            address = find_address_like(snippets)
            if phone or address:
                return NameResult(first_name=first, last_name=last, address=address or "N/A", phone=phone or "N/A", source="DuckDuckGo")
    except Exception:
        pass
    return None

SOCIALS = {
    "Facebook": "https://www.facebook.com/{u}",
    "Twitter": "https://x.com/{u}",
    "Instagram": "https://www.instagram.com/{u}",
    "LinkedIn": "https://www.linkedin.com/in/{u}",
    "GitHub": "https://github.com/{u}",
    "Reddit": "https://www.reddit.com/user/{u}",
    "TikTok": "https://www.tiktok.com/@{u}",
    "Pinterest": "https://www.pinterest.com/{u}",
    "Medium": "https://medium.com/@{u}",
}

def check_username(username: str, min_services: int = 5) -> UsernameResult:
    u = normalize_username(username)
    results: Dict[str, bool] = {}
    for name, pattern in SOCIALS.items():
        url = pattern.format(u=u)
        exists = False
        try:
            resp = requests.get(url, headers={"User-Agent": UA}, timeout=TIMEOUT, allow_redirects=True)
            # Heuristics: 200 OK with some content usually means present
            # Some services return 302 to login if exists; treat 200/301/302/307 as "maybe exists" and refine by common "not found" markers
            if resp.status_code in (200, 301, 302, 303, 307, 308):
                txt = resp.text.lower()
                not_found_markers = ["not found", "page inexistante", "doesn’t exist", "doesn't exist", "404"]
                if any(k in txt for k in not_found_markers):
                    exists = False
                else:
                    exists = True
        except Exception:
            exists = False
        results[name] = exists
        # Be polite
        time.sleep(0.2)
    return UsernameResult(username=u, services=results)

# -------------------- CLI --------------------

def cli():
    parser = argparse.ArgumentParser(
        prog="passive",
        description="Welcome to passive v1.0.0 — passive OSINT helper",
        formatter_class=argparse.RawTextHelpFormatter,
    )
    g = parser.add_mutually_exclusive_group(required=True)
    g.add_argument("-fn", dest="full_name", help='Search with full-name, e.g. "Jean Dupont"')
    g.add_argument("-ip", dest="ip", help="Search with IP address, e.g. *******")
    g.add_argument("-u", dest="username", help='Search with username, e.g. "@user01"')

    args = parser.parse_args()

    if args.full_name:
        if not is_full_name(args.full_name):
            print("Input does not look like a full name. Example: Jean Dupont", file=sys.stderr)
            sys.exit(2)
        result = name_lookup(args.full_name)
        if not result:
            print("No public directory hit found for that name.", file=sys.stderr)
            sys.exit(3)
        print(f"First name: {result.first_name}")
        print(f"Last name: {result.last_name}")
        print(f"Address: {result.address}")
        print(f"Number: {result.phone}")
        out = f"First name: {result.first_name}\nLast name: {result.last_name}\nAddress: {result.address}\nNumber: {result.phone}\nSource: {result.source}\n"
        path = save_result(out)
        print(f"Saved in {path.name}")

    elif args.ip:
        if not is_ip(args.ip):
            print("Input does not look like a valid IPv4/IPv6 address.", file=sys.stderr)
            sys.exit(2)
        r = ip_lookup(args.ip)
        if not r:
            print("Could not retrieve IP info from public endpoints.", file=sys.stderr)
            sys.exit(3)
        coord = f"({r.latitude}) / ({r.longitude})" if r.latitude is not None and r.longitude is not None else "N/A"
        print(f"ISP: {r.isp or 'N/A'}")
        print(f"City Lat/Lon:\t{coord}")
        out = f"IP: {r.ip}\nISP: {r.isp or 'N/A'}\nCity: {r.city or 'N/A'}\nLat/Lon: {coord}\nSource: {r.source}\n"
        path = save_result(out)
        print(f"Saved in {path.name}")

    elif args.username:
        if not is_username(args.username):
            print('Input does not look like a username. Example: "@user01"', file=sys.stderr)
            sys.exit(2)
        r = check_username(args.username)
        yes_no = {k: ("yes" if v else "no") for k, v in r.services.items()}
        # Show only 7 common ones in output for brevity/assignment
        shown = ["Facebook", "Twitter", "LinkedIn", "Instagram", "GitHub"]
        for s in shown:
            print(f"{s} : {yes_no.get(s, 'no')}")
        # Save full set
        lines = [f"{svc}: {('yes' if exists else 'no')}" for svc, exists in r.services.items()]
        out = f"User: {r.username}\n" + "\n".join(lines) + "\nSource: " + r.source + "\n"
        path = save_result(out)
        print(f"Saved in {path.name}")

if __name__ == "__main__":
    cli()
