# passive — Passive OSINT CLI (v1.0.0)

> ⚠️ **Educational use only.** Use *passive* only on targets you have *explicit, written permission* to research. Many sites forbid automated scraping — respect their Terms and local laws.

This project helps you practice **passive reconnaissance** (OSINT) by recognizing the input type (Full Name, IP, or Username) and fetching high‑level, public information.

## Features

- `-fn "First Last"` → tries public **telephone directories** (France-first heuristics) and generic search to extract an **address** and **phone number**.
- `-ip <IP>` → uses **public IP geolocation** endpoints (no API key) to display **City** and **ISP** (+ coordinates).
- `-u "@handle"` → checks if a **username** exists across **5+ social networks** by probing public profile URLs.

All results are written to `result.txt` (or `result2.txt`, `result3.txt`, … if files already exist).

## Install

```bash
python -V             # Python 3.9+
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
chmod +x passive.py   # optional convenience
```

Or run directly with `python passive.py`.

## Usage

```text
$> passive --help
Welcome to passive v1.0.0
OPTIONS:
    -fn         Search with full-name
    -ip         Search with ip address
    -u          Search with username
```

### Examples

```bash
$> ./passive.py -fn "Jean Dupont"
First name: Jean
Last name: Dupont
Address: 7 rue du Progrès 75016 Paris
Number: +33601010101
Saved in result.txt

$> ./passive.py -ip 127.0.0.1
ISP: FSociety, S.A.
City Lat/Lon: (13.731) / (-1.1373)
Saved in result2.txt

$> ./passive.py -u "@user01"
Facebook : yes
Twitter : yes
LinkedIn : yes
Instagram : no
GitHub : yes
Saved in result3.txt
```

> The above illustrates the expected **format**. Real results depend on public sources and may differ.

## How it Works (for audit)

### OSINT & Passive Recon (plain English)

- **OSINT** = *Open‑Source Intelligence*: collecting and analyzing **publicly available** information (websites, social media, public databases).
- **Passive reconnaissance** means **no direct interaction** with the target systems/services beyond normal web browsing — e.g., looking up an IP in a geo‑IP DB, checking a public profile page, reading a directory listing. It excludes intrusive actions like scanning ports or trying credentials.

### Methods used here

1. **Full name (`-fn`)**  
   - Attempts FR **“pages blanches”** style directories first (e.g., PagesJaunes/118000/118712) using standard HTTP GET with a browser‑like User‑Agent.
   - If those fail, falls back to a **privacy‑respectful search engine endpoint** and heuristically extracts **address‑like strings** and **phone patterns** from snippets.
   - Extractors use regex for phone formats and simple line heuristics for addresses. Sources are recorded alongside results.

2. **IP address (`-ip`)**  
   - Queries multiple **free, public geolocation APIs** (ipapi.co, ipinfo.io, ip-api.com).  
   - Normalizes fields to show **City**, **ISP/Org**, and **Lat/Lon**. Records which endpoint returned the data.

3. **Username (`-u`)**  
   - Builds well‑known **public profile URLs** (e.g., `https://github.com/<user>`).  
   - Uses HTTP GET to see if the page appears to exist (status and content checks).  
   - Reports **yes/no** across at least 5 networks and stores the full set.

### Why these choices?

- They rely solely on **public** data, require **no API keys**, and **avoid intrusive behavior**.
- Multiple fallbacks increase robustness; polite delays (`time.sleep(0.2)`) reduce load.

## Passing the Audit

- **Explain OSINT & methods** → See the "How it Works" section above.
- **Explain program flow** → See `passive.py`, functions:
  - `is_full_name`, `is_ip`, `is_username` classify inputs.
  - `name_lookup` → tries FR directories then a generic search; extracts `address` and `phone`.
  - `ip_lookup` → tries 3 endpoints; outputs City/ISP/Lat/Lon.
  - `check_username` → probes ≥5 networks and saves results.
- **Required repo files** → source code + `README.md` (+ `requirements.txt` provided).
- **Tests** → Run the three flags as in “Examples.” Each run writes a `result*.txt`.

## Notes, Limits & Ethics

- Real-world directories differ by **country** and may block bots. You can **add local providers** easily: implement another function like `directory_lookup_fr`.
- Some social sites aggressively redirect unauthenticated users; the checker uses heuristics (status + common "not found" phrases).
- Be respectful: small delays are added; consider your jurisdiction’s laws and each site’s ToS.

## Extending (Bonus ideas)

- Add more **country‑specific** directory scrapers.
- Add **API key** options (e.g., `--ipinfo-token`) and support richer fields.
- Add output formats: `--json` / `--csv`.
- Add a `--source` flag to print the exact URLs used.
- Containerize with a minimal `Dockerfile`.

## License

MIT — see `LICENSE`.
